#!/usr/bin/env tsx
/**
 * Example script demonstrating how to use LLMClassifier in Braintrust (TypeScript).
 * 
 * LLMClassifier is implemented as part of the scorer system, allowing you to create
 * LLM-based scorers that classify outputs using chain-of-thought reasoning.
 */

import { Eval, initLogger } from "braintrust";
import { Project } from "braintrust/framework2";

// Initialize Braintrust logger
initLogger({ projectName: "llm-classifier-example-ts" });

/**
 * Example 1: Create a sentiment classification scorer using LLMClassifier.
 */
function createSentimentClassifierExample() {
  // Create a project
  const project = new Project("sentiment-analysis-ts");
  
  // Create an LLM-based scorer using the LLMClassifier
  const sentimentScorer = project.scorers.create({
    name: "sentiment_classifier",
    description: "Classifies text sentiment as positive, negative, or neutral",
    prompt: `You are a sentiment analysis expert. Analyze the given text and classify its sentiment.

Text to analyze: {output}

Think step by step about the emotional tone, word choice, and overall sentiment expressed in the text.

Based on your analysis, classify the sentiment as one of:
- Positive: The text expresses positive emotions, satisfaction, or optimism
- Negative: The text expresses negative emotions, dissatisfaction, or pessimism  
- Neutral: The text is factual, balanced, or doesn't express clear sentiment

Provide your classification:`,
    model: "gpt-4o-mini",
    useCot: true, // Enable chain-of-thought reasoning
    choiceScores: {
      "Positive": 1.0,
      "Negative": 0.0,
      "Neutral": 0.5
    }
  });
  
  return sentimentScorer;
}

/**
 * Example 2: Create a response quality classifier using chat messages format.
 */
function createQualityClassifierExample() {
  const project = new Project("response-quality-ts");
  
  // Create an LLM-based scorer using chat messages format
  const qualityScorer = project.scorers.create({
    name: "response_quality_classifier",
    description: "Classifies response quality as excellent, good, fair, or poor",
    messages: [
      {
        role: "system",
        content: "You are an expert evaluator of AI assistant responses. Your job is to assess the quality of responses based on accuracy, helpfulness, clarity, and completeness."
      },
      {
        role: "user", 
        content: `Please evaluate this AI assistant response:

Input Question: {input}
AI Response: {output}
Expected Answer: {expected}

Consider the following criteria:
1. Accuracy: Is the information correct?
2. Helpfulness: Does it address the user's question?
3. Clarity: Is it easy to understand?
4. Completeness: Does it provide sufficient detail?

Think through each criterion step by step, then classify the overall quality as:
- Excellent: Exceeds expectations in all areas
- Good: Meets expectations with minor room for improvement
- Fair: Adequate but has notable shortcomings
- Poor: Fails to meet basic expectations

Your classification:`
      }
    ],
    model: "gpt-4o",
    useCot: true,
    choiceScores: {
      "Excellent": 1.0,
      "Good": 0.75,
      "Fair": 0.5,
      "Poor": 0.0
    }
  });
  
  return qualityScorer;
}

/**
 * Run an evaluation using the sentiment classifier.
 */
function runSentimentEvaluation() {
  // Sample data for sentiment analysis
  const testData = [
    {
      input: "How was your experience?",
      output: "I absolutely loved it! The service was fantastic and exceeded all my expectations.",
      expected: "Positive"
    },
    {
      input: "What did you think of the movie?",
      output: "It was terrible. The plot made no sense and the acting was awful.",
      expected: "Negative"
    },
    {
      input: "How's the weather today?",
      output: "It's 72 degrees and partly cloudy.",
      expected: "Neutral"
    },
    {
      input: "Tell me about your day",
      output: "I'm so frustrated! Everything went wrong and nothing worked as planned.",
      expected: "Negative"
    }
  ];
  
  // Create the sentiment classifier
  const sentimentScorer = createSentimentClassifierExample();
  
  // Run evaluation
  const evalResult = Eval("Sentiment Classification Test", {
    data: () => testData,
    task: (input: string) => input, // Pass through the input since we're evaluating the output
    scores: [sentimentScorer]
  });
  
  return evalResult;
}

/**
 * Run an evaluation using the quality classifier.
 */
function runQualityEvaluation() {
  // Sample data for quality assessment
  const testData = [
    {
      input: "What is the capital of France?",
      output: "The capital of France is Paris. It's located in the north-central part of the country and is known for landmarks like the Eiffel Tower and Louvre Museum.",
      expected: "Paris"
    },
    {
      input: "How do I bake a chocolate cake?",
      output: "Mix ingredients and bake.",
      expected: "A detailed recipe with ingredients, measurements, and step-by-step instructions"
    },
    {
      input: "What causes rain?",
      output: "Rain is caused by the water cycle. When water evaporates from oceans, lakes, and rivers, it rises into the atmosphere where it cools and condenses into clouds. When the water droplets in clouds become too heavy, they fall as precipitation.",
      expected: "An explanation of the water cycle and precipitation process"
    }
  ];
  
  // Create the quality classifier
  const qualityScorer = createQualityClassifierExample();
  
  // Run evaluation
  const evalResult = Eval("Response Quality Assessment", {
    data: () => testData,
    task: (input: string) => input, // Pass through since we're evaluating the output
    scores: [qualityScorer]
  });
  
  return evalResult;
}

/**
 * Main function to demonstrate LLMClassifier usage.
 */
async function main() {
  // Check if API key is set
  if (!process.env.BRAINTRUST_API_KEY) {
    console.log("Warning: BRAINTRUST_API_KEY environment variable not set.");
    console.log("Set it with: export BRAINTRUST_API_KEY=<your-api-key>");
    return;
  }
  
  console.log("🚀 LLMClassifier Examples (TypeScript)");
  console.log("=" + "=".repeat(49));
  
  console.log("\n1. Creating Sentiment Classifier...");
  const sentimentScorer = createSentimentClassifierExample();
  console.log(`✅ Created sentiment classifier: ${sentimentScorer.name}`);
  
  console.log("\n2. Creating Quality Classifier...");
  const qualityScorer = createQualityClassifierExample();
  console.log(`✅ Created quality classifier: ${qualityScorer.name}`);
  
  console.log("\n3. Running Sentiment Analysis Evaluation...");
  try {
    const sentimentResult = runSentimentEvaluation();
    console.log("✅ Sentiment evaluation completed!");
  } catch (error) {
    console.log(`❌ Sentiment evaluation failed: ${error}`);
  }
  
  console.log("\n4. Running Quality Assessment Evaluation...");
  try {
    const qualityResult = runQualityEvaluation();
    console.log("✅ Quality evaluation completed!");
  } catch (error) {
    console.log(`❌ Quality evaluation failed: ${error}`);
  }
  
  console.log("\n🎉 All examples completed!");
  console.log("\nKey LLMClassifier Parameters:");
  console.log("- prompt/messages: The prompt template for classification");
  console.log("- model: The LLM model to use (e.g., 'gpt-4o', 'gpt-4o-mini')");
  console.log("- useCot: Enable chain-of-thought reasoning (true/false)");
  console.log("- choiceScores: Object mapping classification choices to scores (0.0-1.0)");
}

// Run the main function
main().catch(console.error);
