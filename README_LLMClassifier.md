# LLMClassifier Examples

This directory contains example scripts demonstrating how to use LLMClassifier in Braintrust for both Python and TypeScript.

## What is LLMClassifier?

LLMClassifier is a feature in Braintrust that allows you to create LLM-based scorers for classification tasks. It uses Large Language Models to classify outputs into predefined categories with associated scores, optionally using chain-of-thought reasoning for better accuracy.

## Key Features

- **Chain-of-Thought Reasoning**: Enable `use_cot`/`useCot` to have the LLM think step-by-step before classification
- **Flexible Scoring**: Map classification choices to custom scores (0.0-1.0)
- **Multiple Formats**: Support both simple prompts and chat message formats
- **Model Agnostic**: Works with various LLM models (GPT-4, GPT-3.5, etc.)

## Files

- `llm_classifier_example.py` - Python implementation with sentiment and quality classification examples
- `llm_classifier_example.ts` - TypeScript implementation with the same examples
- `README_LLMClassifier.md` - This documentation file

## Prerequisites

1. **Install Braintrust SDK**:
   ```bash
   # Python
   pip install braintrust
   
   # TypeScript/Node.js
   npm install braintrust
   ```

2. **Set API Key**:
   ```bash
   export BRAINTRUST_API_KEY=<your-braintrust-api-key>
   ```

3. **For TypeScript**: Install tsx for running TypeScript files:
   ```bash
   npm install -g tsx
   ```

## Running the Examples

### Python
```bash
python llm_classifier_example.py
```

### TypeScript
```bash
tsx llm_classifier_example.ts
# or
npx tsx llm_classifier_example.ts
```

## Example Use Cases

### 1. Sentiment Classification
Classifies text sentiment as Positive, Negative, or Neutral with corresponding scores:
- Positive: 1.0
- Negative: 0.0  
- Neutral: 0.5

### 2. Response Quality Assessment
Evaluates AI assistant responses on multiple criteria:
- Excellent: 1.0
- Good: 0.75
- Fair: 0.5
- Poor: 0.0

## Key Parameters

### Python (`project.scorers.create()`)
```python
scorer = project.scorers.create(
    name="classifier_name",
    description="Description of what this classifier does",
    prompt="Your classification prompt with {input}, {output}, {expected} placeholders",
    # OR use messages for chat format:
    # messages=[{"role": "system", "content": "..."}, {"role": "user", "content": "..."}],
    model="gpt-4o-mini",  # or "gpt-4o", "gpt-3.5-turbo", etc.
    use_cot=True,  # Enable chain-of-thought reasoning
    choice_scores={
        "Choice1": 1.0,
        "Choice2": 0.5,
        "Choice3": 0.0
    }
)
```

### TypeScript (`project.scorers.create()`)
```typescript
const scorer = project.scorers.create({
    name: "classifier_name",
    description: "Description of what this classifier does",
    prompt: "Your classification prompt with {input}, {output}, {expected} placeholders",
    // OR use messages for chat format:
    // messages: [{role: "system", content: "..."}, {role: "user", content: "..."}],
    model: "gpt-4o-mini",  // or "gpt-4o", "gpt-3.5-turbo", etc.
    useCot: true,  // Enable chain-of-thought reasoning
    choiceScores: {
        "Choice1": 1.0,
        "Choice2": 0.5,
        "Choice3": 0.0
    }
});
```

## Template Variables

In your prompts, you can use these template variables:
- `{input}` - The input to the task being evaluated
- `{output}` - The output from the task being evaluated  
- `{expected}` - The expected/reference output (if available)

## Best Practices

1. **Clear Instructions**: Provide clear, specific instructions for classification
2. **Chain-of-Thought**: Enable `use_cot`/`useCot` for complex classification tasks
3. **Meaningful Scores**: Map classification choices to scores that reflect their relative quality/correctness
4. **Consistent Choices**: Use consistent choice names across similar classifiers
5. **Test with Examples**: Include diverse test cases to validate classifier behavior

## Troubleshooting

1. **API Key Issues**: Ensure `BRAINTRUST_API_KEY` is set correctly
2. **Model Access**: Verify you have access to the specified model
3. **Choice Scores**: Ensure all choice_scores values are between 0.0 and 1.0
4. **Template Variables**: Check that your prompt uses the correct variable names ({input}, {output}, {expected})

## Next Steps

- Experiment with different prompt templates
- Try different models to see performance differences
- Adjust choice_scores based on your specific use case
- Integrate classifiers into your evaluation pipelines
- Use the Braintrust web interface to view detailed results
