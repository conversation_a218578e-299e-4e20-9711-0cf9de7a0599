#!/usr/bin/env python3
"""
Example script demonstrating how to use LLMClassifier in Braintrust.

LLMClassifier is implemented as part of the scorer system, allowing you to create
LLM-based scorers that classify outputs using chain-of-thought reasoning.
"""

import os
from braintrust import Eval, init_logger
from braintrust.framework2 import Project

# Initialize Braintrust logger
init_logger(project="llm-classifier-example")

def create_sentiment_classifier_example():
    """Example 1: Create a sentiment classification scorer using LLMClassifier."""
    
    # Create a project
    project = Project("sentiment-analysis")
    
    # Create an LLM-based scorer using the LLMClassifier
    sentiment_scorer = project.scorers.create(
        name="sentiment_classifier",
        description="Classifies text sentiment as positive, negative, or neutral",
        prompt="""You are a sentiment analysis expert. Analyze the given text and classify its sentiment.

Text to analyze: {output}

Think step by step about the emotional tone, word choice, and overall sentiment expressed in the text.

Based on your analysis, classify the sentiment as one of:
- Positive: The text expresses positive emotions, satisfaction, or optimism
- Negative: The text expresses negative emotions, dissatisfaction, or pessimism  
- Neutral: The text is factual, balanced, or doesn't express clear sentiment

Provide your classification:""",
        model="gpt-4o-mini",
        use_cot=True,  # Enable chain-of-thought reasoning
        choice_scores={
            "Positive": 1.0,
            "Negative": 0.0,
            "Neutral": 0.5
        }
    )
    
    return sentiment_scorer

def create_quality_classifier_example():
    """Example 2: Create a response quality classifier using chat messages format."""
    
    project = Project("response-quality")
    
    # Create an LLM-based scorer using chat messages format
    quality_scorer = project.scorers.create(
        name="response_quality_classifier",
        description="Classifies response quality as excellent, good, fair, or poor",
        messages=[
            {
                "role": "system",
                "content": "You are an expert evaluator of AI assistant responses. Your job is to assess the quality of responses based on accuracy, helpfulness, clarity, and completeness."
            },
            {
                "role": "user", 
                "content": """Please evaluate this AI assistant response:

Input Question: {input}
AI Response: {output}
Expected Answer: {expected}

Consider the following criteria:
1. Accuracy: Is the information correct?
2. Helpfulness: Does it address the user's question?
3. Clarity: Is it easy to understand?
4. Completeness: Does it provide sufficient detail?

Think through each criterion step by step, then classify the overall quality as:
- Excellent: Exceeds expectations in all areas
- Good: Meets expectations with minor room for improvement
- Fair: Adequate but has notable shortcomings
- Poor: Fails to meet basic expectations

Your classification:"""
            }
        ],
        model="gpt-4o",
        use_cot=True,
        choice_scores={
            "Excellent": 1.0,
            "Good": 0.75,
            "Fair": 0.5,
            "Poor": 0.0
        }
    )
    
    return quality_scorer

def run_sentiment_evaluation():
    """Run an evaluation using the sentiment classifier."""
    
    # Sample data for sentiment analysis
    test_data = [
        {
            "input": "How was your experience?",
            "output": "I absolutely loved it! The service was fantastic and exceeded all my expectations.",
            "expected": "Positive"
        },
        {
            "input": "What did you think of the movie?",
            "output": "It was terrible. The plot made no sense and the acting was awful.",
            "expected": "Negative"
        },
        {
            "input": "How's the weather today?",
            "output": "It's 72 degrees and partly cloudy.",
            "expected": "Neutral"
        },
        {
            "input": "Tell me about your day",
            "output": "I'm so frustrated! Everything went wrong and nothing worked as planned.",
            "expected": "Negative"
        }
    ]
    
    # Create the sentiment classifier
    sentiment_scorer = create_sentiment_classifier_example()
    
    # Run evaluation
    eval_result = Eval(
        "Sentiment Classification Test",
        data=lambda: test_data,
        task=lambda input: input,  # Pass through the input since we're evaluating the output
        scores=[sentiment_scorer]
    )
    
    return eval_result

def run_quality_evaluation():
    """Run an evaluation using the quality classifier."""
    
    # Sample data for quality assessment
    test_data = [
        {
            "input": "What is the capital of France?",
            "output": "The capital of France is Paris. It's located in the north-central part of the country and is known for landmarks like the Eiffel Tower and Louvre Museum.",
            "expected": "Paris"
        },
        {
            "input": "How do I bake a chocolate cake?",
            "output": "Mix ingredients and bake.",
            "expected": "A detailed recipe with ingredients, measurements, and step-by-step instructions"
        },
        {
            "input": "What causes rain?",
            "output": "Rain is caused by the water cycle. When water evaporates from oceans, lakes, and rivers, it rises into the atmosphere where it cools and condenses into clouds. When the water droplets in clouds become too heavy, they fall as precipitation.",
            "expected": "An explanation of the water cycle and precipitation process"
        }
    ]
    
    # Create the quality classifier
    quality_scorer = create_quality_classifier_example()
    
    # Run evaluation
    eval_result = Eval(
        "Response Quality Assessment",
        data=lambda: test_data,
        task=lambda input: input,  # Pass through since we're evaluating the output
        scores=[quality_scorer]
    )
    
    return eval_result

def main():
    """Main function to demonstrate LLMClassifier usage."""
    
    # Check if API key is set
    if not os.getenv("BRAINTRUST_API_KEY"):
        print("Warning: BRAINTRUST_API_KEY environment variable not set.")
        print("Set it with: export BRAINTRUST_API_KEY=<your-api-key>")
        return
    
    print("🚀 LLMClassifier Examples")
    print("=" * 50)
    
    print("\n1. Creating Sentiment Classifier...")
    sentiment_scorer = create_sentiment_classifier_example()
    print(f"✅ Created sentiment classifier: {sentiment_scorer.name}")
    
    print("\n2. Creating Quality Classifier...")
    quality_scorer = create_quality_classifier_example()
    print(f"✅ Created quality classifier: {quality_scorer.name}")
    
    print("\n3. Running Sentiment Analysis Evaluation...")
    try:
        sentiment_result = run_sentiment_evaluation()
        print("✅ Sentiment evaluation completed!")
    except Exception as e:
        print(f"❌ Sentiment evaluation failed: {e}")
    
    print("\n4. Running Quality Assessment Evaluation...")
    try:
        quality_result = run_quality_evaluation()
        print("✅ Quality evaluation completed!")
    except Exception as e:
        print(f"❌ Quality evaluation failed: {e}")
    
    print("\n🎉 All examples completed!")
    print("\nKey LLMClassifier Parameters:")
    print("- prompt/messages: The prompt template for classification")
    print("- model: The LLM model to use (e.g., 'gpt-4o', 'gpt-4o-mini')")
    print("- use_cot: Enable chain-of-thought reasoning (True/False)")
    print("- choice_scores: Dict mapping classification choices to scores (0.0-1.0)")

if __name__ == "__main__":
    main()
