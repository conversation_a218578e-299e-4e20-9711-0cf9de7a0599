#!/usr/bin/env python3
"""
Example script demonstrating how to use LLMClassifier in Braintrust.

LLMClassifier is implemented as part of the scorer system, allowing you to create
LLM-based scorers that classify outputs using chain-of-thought reasoning.
"""

import os
from braintrust import Eval, init_logger

# Initialize Braintrust logger
init_logger(project="llm-classifier-example")

def sentiment_classifier_scorer(output, expected=None, input=None, **kwargs):
    """
    A simple function-based scorer that demonstrates LLM classification logic.
    In practice, you would use the LLMClassifier through the framework2 API,
    but this shows the concept in a way that works with the traditional Eval API.
    """
    # This is a simplified example - in reality, you'd call an LLM here
    # For demonstration, we'll do simple keyword-based classification

    output_lower = output.lower()

    # Simple sentiment analysis based on keywords
    positive_words = ['love', 'fantastic', 'great', 'excellent', 'amazing', 'wonderful']
    negative_words = ['terrible', 'awful', 'hate', 'bad', 'horrible', 'frustrated']

    positive_count = sum(1 for word in positive_words if word in output_lower)
    negative_count = sum(1 for word in negative_words if word in output_lower)

    if positive_count > negative_count:
        classification = "Positive"
        score = 1.0
    elif negative_count > positive_count:
        classification = "Negative"
        score = 0.0
    else:
        classification = "Neutral"
        score = 0.5

    return {
        "name": "sentiment_classifier",
        "score": score,
        "metadata": {
            "classification": classification,
            "positive_words_found": positive_count,
            "negative_words_found": negative_count
        }
    }

def quality_classifier_scorer(output, expected=None, input=None, **kwargs):
    """
    A simple function-based scorer that demonstrates quality classification logic.
    This is a simplified version - in practice, you'd use the LLMClassifier through
    the framework2 API or call an LLM directly.
    """
    # Simple quality assessment based on response length and content
    output_length = len(output.split())

    # Basic quality assessment
    if output_length < 5:
        classification = "Poor"
        score = 0.0
    elif output_length < 15:
        classification = "Fair"
        score = 0.5
    elif output_length < 30:
        classification = "Good"
        score = 0.75
    else:
        classification = "Excellent"
        score = 1.0

    # Adjust score based on expected answer if provided
    if expected and expected.lower() in output.lower():
        score = min(1.0, score + 0.2)  # Boost score if expected content is present

    return {
        "name": "quality_classifier",
        "score": score,
        "metadata": {
            "classification": classification,
            "word_count": output_length,
            "contains_expected": expected.lower() in output.lower() if expected else False
        }
    }

def run_sentiment_evaluation():
    """Run an evaluation using the sentiment classifier."""

    # Sample data for sentiment analysis
    test_data = [
        {
            "input": "How was your experience?",
            "output": "I absolutely loved it! The service was fantastic and exceeded all my expectations.",
            "expected": "Positive"
        },
        {
            "input": "What did you think of the movie?",
            "output": "It was terrible. The plot made no sense and the acting was awful.",
            "expected": "Negative"
        },
        {
            "input": "How's the weather today?",
            "output": "It's 72 degrees and partly cloudy.",
            "expected": "Neutral"
        },
        {
            "input": "Tell me about your day",
            "output": "I'm so frustrated! Everything went wrong and nothing worked as planned.",
            "expected": "Negative"
        }
    ]

    # Run evaluation with our sentiment classifier
    eval_result = Eval(
        "Sentiment Classification Test",
        data=lambda: test_data,
        task=lambda input_data: input_data["output"],  # Return the output to be scored
        scores=[sentiment_classifier_scorer]
    )

    return eval_result

def run_quality_evaluation():
    """Run an evaluation using the quality classifier."""

    # Sample data for quality assessment
    test_data = [
        {
            "input": "What is the capital of France?",
            "output": "The capital of France is Paris. It's located in the north-central part of the country and is known for landmarks like the Eiffel Tower and Louvre Museum.",
            "expected": "Paris"
        },
        {
            "input": "How do I bake a chocolate cake?",
            "output": "Mix ingredients and bake.",
            "expected": "A detailed recipe with ingredients, measurements, and step-by-step instructions"
        },
        {
            "input": "What causes rain?",
            "output": "Rain is caused by the water cycle. When water evaporates from oceans, lakes, and rivers, it rises into the atmosphere where it cools and condenses into clouds. When the water droplets in clouds become too heavy, they fall as precipitation.",
            "expected": "An explanation of the water cycle and precipitation process"
        }
    ]

    # Run evaluation with our quality classifier
    eval_result = Eval(
        "Response Quality Assessment",
        data=lambda: test_data,
        task=lambda input_data: input_data["output"],  # Return the output to be scored
        scores=[quality_classifier_scorer]
    )

    return eval_result

def main():
    """Main function to demonstrate LLMClassifier usage."""

    # Check if API key is set
    if not os.getenv("BRAINTRUST_API_KEY"):
        print("Warning: BRAINTRUST_API_KEY environment variable not set.")
        print("Set it with: export BRAINTRUST_API_KEY=<your-api-key>")
        return

    print("🚀 LLMClassifier Examples")
    print("=" * 50)

    print("\nThis example demonstrates the concept of LLMClassifier using simplified scorers.")
    print("In production, you would use the framework2 API with actual LLM calls.")

    print("\n1. Running Sentiment Analysis Evaluation...")
    try:
        sentiment_result = run_sentiment_evaluation()
        print("✅ Sentiment evaluation completed!")
    except Exception as e:
        print(f"❌ Sentiment evaluation failed: {e}")

    print("\n2. Running Quality Assessment Evaluation...")
    try:
        quality_result = run_quality_evaluation()
        print("✅ Quality evaluation completed!")
    except Exception as e:
        print(f"❌ Quality evaluation failed: {e}")

    print("\n🎉 All examples completed!")
    print("\nKey LLMClassifier Concepts Demonstrated:")
    print("- Classification scoring: Mapping outputs to discrete categories")
    print("- Score assignment: Each category gets a score between 0.0-1.0")
    print("- Metadata tracking: Additional information about the classification")
    print("- Evaluation integration: Using classifiers in Braintrust evaluations")
    print("\nFor production use, implement actual LLM calls in your scorer functions.")

if __name__ == "__main__":
    main()
