#!/usr/bin/env python3
"""
Working example of LLMClassifier in Braintrust using the correct API.

This example shows how to create LLM-based classifiers that actually call LLMs
to perform classification with chain-of-thought reasoning.
"""

import os
from braintrust import Eva<PERSON>, init_logger
from openai import OpenAI

# Initialize Braintrust logger
init_logger(project="llm-classifier-working-example")

# Initialize OpenAI client (you can also use other LLM providers)
openai_client = OpenAI()

def create_llm_sentiment_classifier():
    """
    Create an LLM-based sentiment classifier function.
    This demonstrates the core concept of LLMClassifier by implementing it manually.
    """
    def sentiment_classifier(output, expected=None, input=None, **kwargs):
        """LLM-based sentiment classifier with chain-of-thought reasoning."""
        
        # Construct the prompt with chain-of-thought reasoning
        prompt = f"""You are a sentiment analysis expert. Analyze the given text and classify its sentiment.

Text to analyze: {output}

Think step by step about the emotional tone, word choice, and overall sentiment expressed in the text.

Based on your analysis, classify the sentiment as one of:
- Positive: The text expresses positive emotions, satisfaction, or optimism
- Negative: The text expresses negative emotions, dissatisfaction, or pessimism  
- Neutral: The text is factual, balanced, or doesn't express clear sentiment

Think through your reasoning step by step, then provide your final classification as exactly one of: Positive, Negative, or Neutral.

Your analysis and classification:"""

        try:
            # Call the LLM
            response = openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,  # Low temperature for consistent classification
                max_tokens=200
            )
            
            llm_response = response.choices[0].message.content.strip()
            
            # Extract classification from response
            classification = "Neutral"  # Default
            if "Positive" in llm_response:
                classification = "Positive"
            elif "Negative" in llm_response:
                classification = "Negative"
            elif "Neutral" in llm_response:
                classification = "Neutral"
            
            # Map classification to scores (this is the key LLMClassifier concept)
            choice_scores = {
                "Positive": 1.0,
                "Negative": 0.0,
                "Neutral": 0.5
            }
            
            score = choice_scores.get(classification, 0.5)
            
            return {
                "name": "llm_sentiment_classifier",
                "score": score,
                "metadata": {
                    "classification": classification,
                    "llm_reasoning": llm_response,
                    "model_used": "gpt-4o-mini"
                }
            }
            
        except Exception as e:
            print(f"Error in LLM classification: {e}")
            return {
                "name": "llm_sentiment_classifier",
                "score": 0.5,  # Default neutral score on error
                "metadata": {
                    "classification": "Error",
                    "error": str(e)
                }
            }
    
    return sentiment_classifier

def create_llm_quality_classifier():
    """
    Create an LLM-based quality classifier function.
    This demonstrates multi-criteria evaluation with chain-of-thought.
    """
    def quality_classifier(output, expected=None, input=None, **kwargs):
        """LLM-based quality classifier with detailed evaluation criteria."""
        
        # Construct the evaluation prompt
        prompt = f"""You are an expert evaluator of AI assistant responses. Your job is to assess the quality of responses based on accuracy, helpfulness, clarity, and completeness.

Input Question: {input if input else "N/A"}
AI Response: {output}
Expected Answer: {expected if expected else "N/A"}

Consider the following criteria:
1. Accuracy: Is the information correct?
2. Helpfulness: Does it address the user's question?
3. Clarity: Is it easy to understand?
4. Completeness: Does it provide sufficient detail?

Think through each criterion step by step, then classify the overall quality as exactly one of:
- Excellent: Exceeds expectations in all areas
- Good: Meets expectations with minor room for improvement
- Fair: Adequate but has notable shortcomings
- Poor: Fails to meet basic expectations

Provide your detailed analysis and final classification:"""

        try:
            # Call the LLM
            response = openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": "You are an expert evaluator of AI responses. Be thorough but concise in your analysis."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=300
            )
            
            llm_response = response.choices[0].message.content.strip()
            
            # Extract classification from response
            classification = "Fair"  # Default
            if "Excellent" in llm_response:
                classification = "Excellent"
            elif "Good" in llm_response:
                classification = "Good"
            elif "Fair" in llm_response:
                classification = "Fair"
            elif "Poor" in llm_response:
                classification = "Poor"
            
            # Map classification to scores
            choice_scores = {
                "Excellent": 1.0,
                "Good": 0.75,
                "Fair": 0.5,
                "Poor": 0.0
            }
            
            score = choice_scores.get(classification, 0.5)
            
            return {
                "name": "llm_quality_classifier",
                "score": score,
                "metadata": {
                    "classification": classification,
                    "llm_reasoning": llm_response,
                    "model_used": "gpt-4o-mini"
                }
            }
            
        except Exception as e:
            print(f"Error in LLM classification: {e}")
            return {
                "name": "llm_quality_classifier",
                "score": 0.5,
                "metadata": {
                    "classification": "Error",
                    "error": str(e)
                }
            }
    
    return quality_classifier

def run_sentiment_evaluation():
    """Run sentiment evaluation with actual LLM classification."""
    
    test_data = [
        {
            "input": "How was your experience?",
            "output": "I absolutely loved it! The service was fantastic and exceeded all my expectations.",
            "expected": "Positive"
        },
        {
            "input": "What did you think of the movie?",
            "output": "It was terrible. The plot made no sense and the acting was awful.",
            "expected": "Negative"
        },
        {
            "input": "How's the weather today?",
            "output": "It's 72 degrees and partly cloudy.",
            "expected": "Neutral"
        },
        {
            "input": "Tell me about your day",
            "output": "I'm so frustrated! Everything went wrong and nothing worked as planned.",
            "expected": "Negative"
        }
    ]
    
    # Create the LLM classifier
    sentiment_classifier = create_llm_sentiment_classifier()
    
    # Run evaluation
    eval_result = Eval(
        "LLM Sentiment Classification Test",
        data=lambda: test_data,
        task=lambda input_data: input_data["output"],
        scores=[sentiment_classifier]
    )
    
    return eval_result

def run_quality_evaluation():
    """Run quality evaluation with actual LLM classification."""
    
    test_data = [
        {
            "input": "What is the capital of France?",
            "output": "The capital of France is Paris. It's located in the north-central part of the country and is known for landmarks like the Eiffel Tower and Louvre Museum.",
            "expected": "Paris"
        },
        {
            "input": "How do I bake a chocolate cake?",
            "output": "Mix ingredients and bake.",
            "expected": "A detailed recipe with ingredients, measurements, and step-by-step instructions"
        },
        {
            "input": "What causes rain?",
            "output": "Rain is caused by the water cycle. When water evaporates from oceans, lakes, and rivers, it rises into the atmosphere where it cools and condenses into clouds. When the water droplets in clouds become too heavy, they fall as precipitation.",
            "expected": "An explanation of the water cycle and precipitation process"
        }
    ]
    
    # Create the LLM classifier
    quality_classifier = create_llm_quality_classifier()
    
    # Run evaluation
    eval_result = Eval(
        "LLM Quality Assessment Test",
        data=lambda: test_data,
        task=lambda input_data: input_data["output"],
        scores=[quality_classifier]
    )
    
    return eval_result

def main():
    """Main function demonstrating working LLMClassifier examples."""
    
    # Check for required API keys
    if not os.getenv("BRAINTRUST_API_KEY"):
        print("❌ BRAINTRUST_API_KEY environment variable not set.")
        print("Set it with: export BRAINTRUST_API_KEY=<your-api-key>")
        return
    
    if not os.getenv("OPENAI_API_KEY"):
        print("❌ OPENAI_API_KEY environment variable not set.")
        print("Set it with: export OPENAI_API_KEY=<your-openai-api-key>")
        return
    
    print("🚀 Working LLMClassifier Examples")
    print("=" * 50)
    print("This example uses actual LLM calls to perform classification!")
    
    print("\n1. Running LLM Sentiment Classification...")
    try:
        sentiment_result = run_sentiment_evaluation()
        print("✅ LLM sentiment evaluation completed!")
        print("   Check the Braintrust dashboard for detailed results and LLM reasoning.")
    except Exception as e:
        print(f"❌ Sentiment evaluation failed: {e}")
    
    print("\n2. Running LLM Quality Assessment...")
    try:
        quality_result = run_quality_evaluation()
        print("✅ LLM quality evaluation completed!")
        print("   Check the Braintrust dashboard for detailed results and LLM reasoning.")
    except Exception as e:
        print(f"❌ Quality evaluation failed: {e}")
    
    print("\n🎉 All LLM classification examples completed!")
    print("\n📊 Key Features Demonstrated:")
    print("- Chain-of-thought reasoning: LLM thinks step-by-step before classifying")
    print("- Choice scoring: Classifications mapped to numerical scores")
    print("- Metadata capture: LLM reasoning and model info stored")
    print("- Error handling: Graceful fallbacks when LLM calls fail")
    print("\n💡 This is the core concept behind Braintrust's LLMClassifier!")

if __name__ == "__main__":
    main()
