#!/usr/bin/env python3
"""
Example script demonstrating how to use LLMClassifier with Braintrust Framework2.

This example shows the proper way to create and use LLM-based classifiers
using the framework2 API, which supports the actual LLMClassifier functionality.
"""

import os
from braintrust.framework2 import Project

def create_llm_classifier_examples():
    """Create examples of LLM-based classifiers using the framework2 API."""
    
    # Create a project
    project = Project("llm-classifier-demo")
    
    # Example 1: Sentiment Classification with simple prompt
    sentiment_classifier = project.scorers.create(
        name="sentiment_classifier",
        description="Classifies text sentiment using LLM with chain-of-thought",
        prompt="""You are a sentiment analysis expert. Analyze the given text and classify its sentiment.

Text to analyze: {output}

Think step by step about the emotional tone, word choice, and overall sentiment expressed in the text.

Based on your analysis, classify the sentiment as one of:
- Positive: The text expresses positive emotions, satisfaction, or optimism
- Negative: The text expresses negative emotions, dissatisfaction, or pessimism  
- Neutral: The text is factual, balanced, or doesn't express clear sentiment

Provide your classification:""",
        model="gpt-4o-mini",
        use_cot=True,  # Enable chain-of-thought reasoning
        choice_scores={
            "Positive": 1.0,
            "Negative": 0.0,
            "Neutral": 0.5
        }
    )
    
    # Example 2: Quality Assessment with chat messages
    quality_classifier = project.scorers.create(
        name="quality_classifier", 
        description="Evaluates response quality using structured chat format",
        messages=[
            {
                "role": "system",
                "content": "You are an expert evaluator of AI responses. Assess quality based on accuracy, helpfulness, clarity, and completeness."
            },
            {
                "role": "user",
                "content": """Evaluate this AI response:

Question: {input}
AI Response: {output}
Expected: {expected}

Rate the quality step by step:
1. Accuracy: Is the information correct?
2. Helpfulness: Does it address the question?
3. Clarity: Is it easy to understand?
4. Completeness: Sufficient detail provided?

Overall quality rating:
- Excellent: Exceeds expectations
- Good: Meets expectations  
- Fair: Adequate with issues
- Poor: Below expectations

Your rating:"""
            }
        ],
        model="gpt-4o",
        use_cot=True,
        choice_scores={
            "Excellent": 1.0,
            "Good": 0.75,
            "Fair": 0.5,
            "Poor": 0.0
        }
    )
    
    # Example 3: Binary Classification (Factual vs Opinion)
    factual_classifier = project.scorers.create(
        name="factual_classifier",
        description="Determines if a statement is factual or opinion-based",
        prompt="""Analyze the following statement and determine if it is primarily factual or opinion-based.

Statement: {output}

Consider:
- Factual: Contains verifiable information, objective data, or established facts
- Opinion: Contains subjective views, personal beliefs, or value judgments

Think through your reasoning, then classify as:
- Factual: The statement is primarily based on verifiable facts
- Opinion: The statement is primarily based on personal views or subjective judgments

Your classification:""",
        model="gpt-4o-mini",
        use_cot=True,
        choice_scores={
            "Factual": 1.0,
            "Opinion": 0.0
        }
    )
    
    return {
        "sentiment": sentiment_classifier,
        "quality": quality_classifier, 
        "factual": factual_classifier,
        "project": project
    }

def demonstrate_classifier_creation():
    """Demonstrate creating LLM classifiers and show their properties."""
    
    print("🚀 Creating LLM Classifiers with Framework2")
    print("=" * 50)
    
    try:
        classifiers = create_llm_classifier_examples()
        
        print(f"\n✅ Created project: {classifiers['project'].name}")
        
        print(f"\n📊 Sentiment Classifier:")
        print(f"   Name: {classifiers['sentiment'].name}")
        print(f"   Description: {classifiers['sentiment'].description}")
        print(f"   Type: LLM Classifier with chain-of-thought")
        
        print(f"\n📊 Quality Classifier:")
        print(f"   Name: {classifiers['quality'].name}")
        print(f"   Description: {classifiers['quality'].description}")
        print(f"   Type: LLM Classifier with chat messages")
        
        print(f"\n📊 Factual Classifier:")
        print(f"   Name: {classifiers['factual'].name}")
        print(f"   Description: {classifiers['factual'].description}")
        print(f"   Type: Binary LLM Classifier")
        
        return classifiers
        
    except Exception as e:
        print(f"❌ Error creating classifiers: {e}")
        return None

def show_classifier_configuration():
    """Show the configuration details of LLM classifiers."""
    
    print("\n🔧 LLMClassifier Configuration Details")
    print("=" * 50)
    
    print("\nKey Parameters:")
    print("• prompt OR messages: The classification prompt template")
    print("• model: LLM model (e.g., 'gpt-4o', 'gpt-4o-mini', 'gpt-3.5-turbo')")
    print("• use_cot: Enable chain-of-thought reasoning (True/False)")
    print("• choice_scores: Dict mapping choices to scores (0.0-1.0)")
    
    print("\nTemplate Variables Available:")
    print("• {input}: The input to the task being evaluated")
    print("• {output}: The output from the task being evaluated")
    print("• {expected}: The expected/reference output (if available)")
    
    print("\nChoice Scores Guidelines:")
    print("• Use 1.0 for the best/correct classification")
    print("• Use 0.0 for the worst/incorrect classification")
    print("• Use intermediate values (0.25, 0.5, 0.75) for gradations")
    print("• Scores should reflect relative quality/correctness")

def main():
    """Main function demonstrating LLMClassifier usage."""
    
    # Check if API key is set
    if not os.getenv("BRAINTRUST_API_KEY"):
        print("⚠️  Warning: BRAINTRUST_API_KEY environment variable not set.")
        print("   Set it with: export BRAINTRUST_API_KEY=<your-api-key>")
        print("   You can get an API key from: https://www.braintrust.dev")
        return
    
    print("🎯 LLMClassifier Framework2 Example")
    print("=" * 50)
    
    # Demonstrate classifier creation
    classifiers = demonstrate_classifier_creation()
    
    if classifiers:
        # Show configuration details
        show_classifier_configuration()
        
        print("\n💡 Next Steps:")
        print("1. Use these classifiers in Eval() calls for evaluation")
        print("2. View results in the Braintrust web interface")
        print("3. Iterate on prompts and choice_scores based on results")
        print("4. Deploy classifiers for production use")
        
        print("\n📚 Example Usage in Evaluation:")
        print("""
# Use the classifier in an evaluation
from braintrust import Eval

eval_result = Eval(
    "My Classification Task",
    data=lambda: your_test_data,
    task=your_task_function,
    scores=[classifiers['sentiment']]  # Use the LLM classifier
)
""")
    
    print("\n🎉 LLMClassifier demonstration completed!")

if __name__ == "__main__":
    main()
